
<!-- DEBUG INFO -->
<!-- Step: ERROR_exception -->
<!-- Description: 异常发生: Message: Reached error page: about:neterror?e=proxyConnectFailure&u=https%3A//auth.augmentcode.com/authorize%3Fresponse_type%3Dcode%26client_id%3Dv%26code_challenge%3DKyf6wpCfMsyCIb75LDRJg45scqZYWY-V9uSTrNMwjoM%26code_challenge_method%3DS256%26state%3DbkpNZfpGke7dGL5EbpVtOQ%26prompt%3Dlogin&c=UTF-8&d=Firefox%20is%20configured%20to%20use%20a%20proxy%20server%20that%20is%20refusing%20connections.
Stacktrace:
RemoteError@chrome://remote/content/shared/RemoteError.sys.mjs:8:8
WebDriverError@chrome://remote/content/shared/webdriver/Errors.sys.mjs:199:5
UnknownError@chrome://remote/content/shared/webdriver/Errors.sys.mjs:910:5
checkReadyState@chrome://remote/content/marionette/navigate.sys.mjs:59:24
onNavigation@chrome://remote/content/marionette/navigate.sys.mjs:348:39
emit@resource://gre/modules/EventEmitter.sys.mjs:156:19
receiveMessage@chrome://remote/content/marionette/actors/MarionetteEventsParent.sys.mjs:33:25
 -->
<!-- URL: https://auth.augmentcode.com/authorize?response_type=code&client_id=v&code_challenge=Kyf6wpCfMsyCIb75LDRJg45scqZYWY-V9uSTrNMwjoM&code_challenge_method=S256&state=bkpNZfpGke7dGL5EbpVtOQ&prompt=login -->
<!-- Timestamp: 2025-08-19T12:13:31.640036 -->
<!-- END DEBUG INFO -->

<html data-l10n-sync="true" lang="en-US" dir="ltr"><head>
    <meta http-equiv="Content-Security-Policy" content="default-src chrome:; object-src 'none'">
    <meta name="color-scheme" content="light dark">
    <title data-l10n-id="neterror-page-title">Problem loading page</title>
    <link rel="stylesheet" href="chrome://global/skin/aboutNetError.css" type="text/css" media="all">
    <link rel="icon" id="favicon" href="chrome://global/skin/icons/info.svg">
    <link rel="localization" href="branding/brand.ftl">
    <link rel="localization" href="toolkit/neterror/certError.ftl">
    <link rel="localization" href="toolkit/neterror/netError.ftl">
  </head>
  <body class="neterror">
    <div class="container">
      <div id="text-container">
        <!-- Error Title -->
        <div class="title">
          <h1 class="title-text" data-l10n-id="proxyConnectFailure-title">The proxy server is refusing connections</h1>
        </div>

        <!-- Short Description -->
        <p id="errorShortDesc">Firefox is configured to use a proxy server that is refusing connections.<p id="response-status-label" data-l10n-args="{&quot;responsestatus&quot;:407,&quot;responsestatustext&quot;:&quot;Proxy Authentication needed&quot;}" data-l10n-id="neterror-response-status-code">Error code: 407 Proxy Authentication needed</p></p>
        <p id="errorShortDesc2"></p>

        <div id="errorWhatToDo" hidden="">
          <p id="errorWhatToDoTitle" data-l10n-id="certerror-what-can-you-do-about-it-title">What can you do about it?</p>
          <p id="badStsCertExplanation" hidden=""></p>
          <p id="errorWhatToDoText"></p>
        </div>

        <!-- Long Description -->
        <div id="errorLongDesc"><ul><li data-l10n-id="neterror-proxy-connect-failure-settings">Check the proxy settings to make sure that they are correct.</li><li data-l10n-id="neterror-proxy-connect-failure-contact-admin">Contact your network administrator to make sure the proxy server is working.</li></ul></div>

        <div id="trrOnlyContainer" hidden="">
          <p id="trrOnlyMessage"></p>
          <div class="trr-message-container">
            <span id="trrOnlyDescription"></span>
            <p id="trrLearnMoreContainer" hidden="">
              <a id="trrOnlylearnMoreLink" target="_blank" rel="noopener noreferrer" data-l10n-id="neterror-learn-more-link">Learn more…</a>
            </p>
          </div>
          <p data-l10n-id="neterror-dns-not-found-trr-third-party-warning2">You can continue with your default DNS resolver. However, a third-party might be able to see what websites you visit.</p>
        </div>

        <p id="tlsVersionNotice" hidden=""></p>

        <p id="learnMoreContainer" hidden="">
          <a id="learnMoreLink" target="_blank" rel="noopener noreferrer" data-telemetry-id="learn_more_link" data-l10n-id="neterror-learn-more-link" href="https://support.mozilla.org/1/firefox/141.0.3/WINNT/en-US/connection-not-secure">Learn more…</a>
        </p>

        <!-- UI for option to report certificate errors to Mozilla. Removed on
             init for other error types .-->
        <div id="prefChangeContainer" class="button-container" hidden="">
          <p data-l10n-id="neterror-pref-reset">It looks like your network security settings might be causing this. Do you want the default settings to be restored?</p>
          <button id="prefResetButton" class="primary" data-l10n-id="neterror-pref-reset-button">Restore default settings</button>
        </div>

        <div id="certErrorAndCaptivePortalButtonContainer" class="button-container" hidden="">
          <button id="returnButton" class="primary" data-telemetry-id="return_button_top" data-l10n-id="neterror-return-to-previous-page-recommended-button">Go Back (Recommended)</button>
          <button id="openPortalLoginPageButton" class="primary" data-l10n-id="neterror-open-portal-login-page-button" hidden="">Open Network Login Page</button>
          <button id="certErrorTryAgainButton" class="primary try-again" data-l10n-id="neterror-try-again-button" hidden="">Try Again</button>
          <button id="advancedButton" data-telemetry-id="advanced_button" data-l10n-id="neterror-advanced-button">Advanced…</button>
        </div>
      </div>

      <div id="netErrorButtonContainer" class="button-container"><button id="neterrorTryAgainButton" class="primary try-again" data-l10n-id="neterror-try-again-button" data-telemetry-id="try_again_button">Try Again</button>
        
        <button id="trrExceptionButton" data-l10n-id="neterror-add-exception-button" data-telemetry-id="add_exception_button" hidden="">Always continue for this site</button>
        <button id="trrSettingsButton" data-l10n-id="neterror-settings-button" data-telemetry-id="settings_button" hidden="">Change DNS settings</button>
      </div>

      <div class="advanced-panel-container">
        <div id="badCertAdvancedPanel" class="advanced-panel" hidden="">
          <p id="badCertTechnicalInfo"></p>
          <a id="viewCertificate" href="javascript:void(0)" data-l10n-id="neterror-view-certificate-link">View Certificate</a>
          <div id="advancedPanelButtonContainer" class="button-container">
            <button id="advancedPanelReturnButton" class="primary" data-telemetry-id="return_button_adv" data-l10n-id="neterror-return-to-previous-page-recommended-button">Go Back (Recommended)</button>
            <button id="advancedPanelTryAgainButton" class="primary try-again" data-l10n-id="neterror-try-again-button" hidden="">Try Again</button>
            <button id="exceptionDialogButton" data-telemetry-id="exception_button" data-l10n-id="neterror-override-exception-button">Accept the Risk and Continue</button>
          </div>
        </div>

        <div id="certificateErrorDebugInformation" class="advanced-panel" hidden="">
          <button id="copyToClipboardTop" data-telemetry-id="clipboard_button_top" data-l10n-id="neterror-copy-to-clipboard-button">Copy text to clipboard</button>
          <div id="certificateErrorText"></div>
          <button id="copyToClipboardBottom" data-telemetry-id="clipboard_button_bot" data-l10n-id="neterror-copy-to-clipboard-button">Copy text to clipboard</button>
        </div>
      </div>
    </div>
    <script src="chrome://global/content/neterror/aboutNetErrorCodes.js"></script>
    <script type="module" src="chrome://global/content/aboutNetError.mjs"></script>
  

</body></html>